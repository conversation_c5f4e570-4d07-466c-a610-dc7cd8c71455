# Script PowerShell pour nettoyer et redémarrer l'application Expo
Write-Host "🧹 Nettoyage de l'application DULU Finance..." -ForegroundColor Yellow

# Arrêter tous les processus Expo en cours
Write-Host "Arrêt des processus Expo..." -ForegroundColor Blue
Get-Process -Name "node" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*expo*" } | Stop-Process -Force

# Nettoyer le cache Metro
Write-Host "Nettoyage du cache Metro..." -ForegroundColor Blue
if (Test-Path ".metro-health-check*") {
    Remove-Item ".metro-health-check*" -Force
}

# Nettoyer le dossier .expo
Write-Host "Nettoyage du dossier .expo..." -ForegroundColor Blue
if (Test-Path ".expo") {
    Remove-Item ".expo" -Recurse -Force
}

# Nettoyer le cache npm
Write-Host "Nettoyage du cache npm..." -ForegroundColor Blue
npm cache clean --force

# Redémarrer l'application avec cache nettoyé
Write-Host "🚀 Redémarrage de l'application..." -ForegroundColor Green
expo start --clear --reset-cache

Write-Host "✅ Nettoyage terminé et application redémarrée!" -ForegroundColor Green
