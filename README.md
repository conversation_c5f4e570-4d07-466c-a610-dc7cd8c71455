# DULU Finance Manager

Application de gestion financière personnelle développée avec Expo et React Native.

## 🚀 Démarrage rapide

### Prérequis
- Node.js (version 18 ou supérieure)
- npm ou yarn
- Expo CLI (`npm install -g @expo/cli`)
- Application Expo Go sur votre téléphone

### Installation
```bash
npm install
```

### Configuration
1. Copiez `.env.example` vers `.env`
2. Configurez vos clés API Supabase et PawaPay dans le fichier `.env`

### Lancement de l'application
```bash
# Démarrage normal
npm run start

# Démarrage avec cache nettoyé
npm run clean

# Démarrage Android avec cache nettoyé
npm run clean-android

# Nettoyage complet et redémarrage
npm run reset
```

## 🔧 Résolution des problèmes

### Problème : Erreurs de permission Android
Si vous voyez des erreurs comme `Permission Denial: registerScreenCaptureObserver`, c'est normal et n'affecte pas le fonctionnement de l'application.

### Problème : "main" has not been registered
1. Arrêtez le serveur Metro
2. Exécutez le script de nettoyage :
   ```bash
   # Sur Windows PowerShell
   .\clean-restart.ps1

   # Ou manuellement
   npm run clean
   ```

### Problème : QR Code ne fonctionne pas
1. Assurez-vous que votre téléphone et ordinateur sont sur le même réseau WiFi
2. Utilisez l'application Expo Go officielle
3. Essayez de redémarrer avec cache nettoyé :
   ```bash
   npm run clean-android
   ```

### Problème : Modules natifs non trouvés
Les modules problématiques sont temporairement désactivés via `temp-disable-modules.js`. C'est normal en développement.

## 📱 Test sur mobile

1. Installez l'application Expo Go sur votre téléphone
2. Lancez `npm run start`
3. Scannez le QR code avec Expo Go (Android) ou l'appareil photo (iOS)

## 🛠️ Scripts disponibles

- `npm run dev` - Démarrage en mode développement
- `npm run start` - Démarrage avec cache nettoyé
- `npm run android` - Démarrage pour Android avec cache nettoyé
- `npm run ios` - Démarrage pour iOS avec cache nettoyé
- `npm run web` - Démarrage pour le web
- `npm run clean` - Nettoyage du cache et redémarrage
- `npm run reset` - Nettoyage complet et réinstallation

## 📁 Structure du projet

```
├── app/                 # Pages de l'application (Expo Router)
├── components/          # Composants réutilisables
├── contexts/           # Contextes React (Auth, Finance, Theme)
├── hooks/              # Hooks personnalisés
├── lib/                # Services (Supabase, PawaPay)
├── constants/          # Constantes (Colors, Layout, Theme)
├── assets/             # Images et ressources
└── types/              # Types TypeScript
```

## 🔑 Variables d'environnement

Configurez ces variables dans votre fichier `.env` :

```env
EXPO_PUBLIC_SUPABASE_URL=your_supabase_url
EXPO_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
EXPO_PUBLIC_PAWAPAY_API_TOKEN=your_pawapay_api_token
EXPO_PUBLIC_PAWAPAY_ENVIRONMENT=sandbox
EXPO_PUBLIC_PAWAPAY_BASE_URL=https://api.sandbox.pawapay.io
```
