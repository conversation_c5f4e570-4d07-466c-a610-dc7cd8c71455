{"expo": {"name": "DULU Finance", "slug": "dulu-finance", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": false, "splash": {"image": "./assets/images/splash.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "com.dulu.finance"}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/icon.png", "backgroundColor": "#ffffff"}, "package": "com.dulu.finance", "permissions": ["android.permission.INTERNET", "android.permission.ACCESS_NETWORK_STATE", "android.permission.CAMERA", "android.permission.RECORD_AUDIO", "android.permission.WRITE_EXTERNAL_STORAGE", "android.permission.READ_EXTERNAL_STORAGE"]}, "web": {"bundler": "metro", "output": "single", "favicon": "./assets/images/favicon.png"}, "plugins": ["expo-router", "expo-font", ["expo-splash-screen", {"image": "./assets/images/splash.png", "imageWidth": 200, "resizeMode": "contain", "backgroundColor": "#ffffff"}]], "experiments": {"typedRoutes": true}}}