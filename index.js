// Point d'entrée personnalisé pour DULU Finance Manager
import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';

// Désactiver les warnings spécifiques sur mobile
if (Platform.OS !== 'web') {
  const originalConsoleWarn = console.warn;
  const originalConsoleError = console.error;
  
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' && 
      (args[0].includes('NativeUnimoduleProxy') || 
       args[0].includes('registerScreenCaptureObserver') ||
       args[0].includes('DETECT_SCREEN_CAPTURE') ||
       args[0].includes('Permission Denial'))
    ) {
      return; // Ignorer ces warnings
    }
    originalConsoleWarn.apply(console, args);
  };

  console.error = (...args) => {
    if (
      typeof args[0] === 'string' && 
      (args[0].includes('NativeUnimoduleProxy') || 
       args[0].includes('registerScreenCaptureObserver') ||
       args[0].includes('DETECT_SCREEN_CAPTURE') ||
       args[0].includes('Permission Denial'))
    ) {
      return; // Ignorer ces erreurs
    }
    originalConsoleError.apply(console, args);
  };
}

// Importer l'application principale
import App from './app/_layout';

// Enregistrer le composant racine
registerRootComponent(App);
