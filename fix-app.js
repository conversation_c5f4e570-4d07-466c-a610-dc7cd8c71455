// Script pour corriger les problèmes de l'application
const fs = require('fs');
const { execSync } = require('child_process');

console.log('🔧 Correction des problèmes de DULU Finance Manager...\n');

try {
  // 1. Nettoyer les caches
  console.log('1. Nettoyage des caches...');
  
  // Supprimer les dossiers de cache
  const cacheDirs = ['.expo', '.metro-health-check*', 'node_modules/.cache'];
  cacheDirs.forEach(dir => {
    try {
      if (fs.existsSync(dir)) {
        execSync(`rmdir /s /q "${dir}"`, { stdio: 'ignore' });
        console.log(`   ✅ ${dir} supprimé`);
      }
    } catch (e) {
      // Ignorer les erreurs
    }
  });

  // 2. Vérifier la configuration
  console.log('\n2. Vérification de la configuration...');
  
  // Vérifier package.json
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  if (packageJson.main !== 'App.js') {
    packageJson.main = 'App.js';
    fs.writeFileSync('package.json', JSON.stringify(packageJson, null, 2));
    console.log('   ✅ package.json corrigé');
  }

  // 3. Nettoyer le cache npm
  console.log('\n3. Nettoyage du cache npm...');
  try {
    execSync('npm cache clean --force', { stdio: 'ignore' });
    console.log('   ✅ Cache npm nettoyé');
  } catch (e) {
    console.log('   ⚠️ Impossible de nettoyer le cache npm');
  }

  console.log('\n✅ Corrections terminées!');
  console.log('\n🚀 Vous pouvez maintenant lancer: npm run start');

} catch (error) {
  console.error('❌ Erreur lors de la correction:', error.message);
}
