const { getDefaultConfig } = require('expo/metro-config');

const config = getDefaultConfig(__dirname);

// Ajouter des extensions de fichiers supportées
config.resolver.assetExts.push(
  // Ajouter d'autres extensions si nécessaire
  'bin'
);

// Configuration pour résoudre les problèmes de modules
config.resolver.platforms = ['native', 'android', 'ios', 'web'];

// Transformer les modules problématiques
config.transformer.getTransformOptions = async () => ({
  transform: {
    experimentalImportSupport: false,
    inlineRequires: true,
  },
});

// Résoudre les problèmes de cache
config.resetCache = true;

module.exports = config;
