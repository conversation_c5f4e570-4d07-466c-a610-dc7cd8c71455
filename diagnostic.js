// Script de diagnostic pour DULU Finance Manager
const fs = require('fs');
const path = require('path');

console.log('🔍 Diagnostic DULU Finance Manager\n');

// Vérifier les fichiers essentiels
const essentialFiles = [
  'package.json',
  'app.json',
  'app/_layout.tsx',
  'metro.config.js',
  '.env',
  'temp-disable-modules.js'
];

console.log('📁 Vérification des fichiers essentiels:');
essentialFiles.forEach(file => {
  const exists = fs.existsSync(file);
  console.log(`  ${exists ? '✅' : '❌'} ${file}`);
});

// Vérifier la configuration package.json
console.log('\n📦 Configuration package.json:');
try {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  console.log(`  ✅ Main entry: ${packageJson.main}`);
  console.log(`  ✅ Scripts disponibles: ${Object.keys(packageJson.scripts).join(', ')}`);
} catch (error) {
  console.log('  ❌ Erreur lors de la lecture de package.json');
}

// Vérifier la configuration app.json
console.log('\n⚙️ Configuration app.json:');
try {
  const appJson = JSON.parse(fs.readFileSync('app.json', 'utf8'));
  console.log(`  ✅ Nom de l'app: ${appJson.expo.name}`);
  console.log(`  ✅ Slug: ${appJson.expo.slug}`);
  console.log(`  ✅ Plugins: ${appJson.expo.plugins.join(', ')}`);
  console.log(`  ✅ Permissions Android: ${appJson.expo.android?.permissions?.length || 0} permissions`);
} catch (error) {
  console.log('  ❌ Erreur lors de la lecture de app.json');
}

// Vérifier les variables d'environnement
console.log('\n🔐 Variables d\'environnement:');
try {
  const envContent = fs.readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n').filter(line => line.trim() && !line.startsWith('#'));
  envLines.forEach(line => {
    const [key] = line.split('=');
    console.log(`  ✅ ${key}`);
  });
} catch (error) {
  console.log('  ❌ Fichier .env non trouvé ou illisible');
}

// Vérifier les dossiers importants
console.log('\n📂 Structure des dossiers:');
const importantDirs = ['app', 'components', 'contexts', 'hooks', 'lib', 'constants', 'assets'];
importantDirs.forEach(dir => {
  const exists = fs.existsSync(dir);
  console.log(`  ${exists ? '✅' : '❌'} ${dir}/`);
});

console.log('\n🎯 Recommandations:');
console.log('  1. Si le QR code ne fonctionne pas, utilisez: npm run clean');
console.log('  2. Pour les erreurs de permission Android, ignorez-les (elles sont normales)');
console.log('  3. Assurez-vous que votre téléphone et PC sont sur le même WiFi');
console.log('  4. Utilisez l\'application Expo Go officielle');

console.log('\n✅ Diagnostic terminé!');
