import { useEffect, useRef } from 'react';
import { Platform } from 'react-native';
// import { Audio } from 'expo-av';

export function useSoundEffects() {
  const isLoadedRef = useRef(false);

  const loadSounds = async () => {
    try {
      // Désactiver temporairement tous les sons pour éviter les erreurs
      console.log('Sons désactivés temporairement');
      isLoadedRef.current = true;
      return;
    } catch (error) {
      console.log('Erreur lors du chargement des sons:', error);
      isLoadedRef.current = true;
    }
  };

  const playNotification = () => {
    console.log('Son de notification désactivé temporairement');
  };

  const playSuccess = () => {
    console.log('Son de succès désactivé temporairement');
  };

  const playError = () => {
    console.log('Son d\'erreur désactivé temporairement');
  };

  const playMessageSent = () => {
    console.log('Son d\'envoi de message désactivé temporairement');
  };

  const playMessageReceived = () => {
    console.log('Son de réception de message désactivé temporairement');
  };

  useEffect(() => {
    loadSounds();
    return () => {
      // Cleanup commenté temporairement
    };
  }, []);

  return {
    playNotification,
    playSuccess,
    playError,
    playMessageSent,
    playMessageReceived,
    isLoaded: isLoadedRef.current,
  };
}


