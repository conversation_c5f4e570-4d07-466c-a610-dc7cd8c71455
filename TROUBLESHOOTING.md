# Guide de dépannage - DULU Finance Manager

## 🚨 Erreurs courantes et solutions

### 1. E<PERSON>ur "Permission Denial: DETECT_SCREEN_CAPTURE"

**Symptômes :**
- Erreur `java.lang.SecurityException: Permission Denial`
- Message `requires android.permission.DETECT_SCREEN_CAPTURE`
- L'application ne se charge pas correctement

**Solutions :**

#### Solution 1 : Utiliser le script de correction
```bash
npm run fix
npm run start
```

#### Solution 2 : Nettoyage manuel
```bash
# Arrêter tous les processus
taskkill /f /im node.exe

# Nettoyer les caches
rmdir /s /q .expo
rmdir /s /q node_modules\.cache

# Redémarrer
npx expo start --clear --reset-cache
```

#### Solution 3 : Utiliser le fichier batch
```bash
# Double-cliquer sur start-app.bat
# Ou exécuter dans le terminal :
start-app.bat
```

### 2. Erreur "main has not been registered"

**Symptômes :**
- Message `"main" has not been registered`
- L'application ne démarre pas
- É<PERSON>ran rouge avec erreur Invariant Violation

**Solutions :**

#### Vérifier le point d'entrée
1. Ouvrir `package.json`
2. Vérifier que `"main": "App.js"`
3. Si différent, corriger et redémarrer

#### Nettoyer complètement
```bash
npm run fix
rm -rf node_modules
npm install
npm run start
```

### 3. QR Code ne fonctionne pas

**Solutions :**
1. Vérifier que le téléphone et PC sont sur le même WiFi
2. Utiliser l'application Expo Go officielle
3. Essayer de redémarrer avec cache nettoyé :
   ```bash
   npm run clean
   ```
4. Si le problème persiste, utiliser la connexion par tunnel :
   ```bash
   npx expo start --tunnel
   ```

### 4. Modules natifs non trouvés

**C'est normal !** Les modules problématiques sont temporairement désactivés via des mocks.

### 5. Erreurs de console persistantes

Si vous voyez encore des erreurs dans la console mobile, c'est probablement normal. L'application devrait quand même fonctionner.

## 🛠️ Scripts de dépannage disponibles

- `npm run fix` - Correction automatique des problèmes
- `npm run diagnostic` - Diagnostic complet du projet
- `npm run clean` - Nettoyage du cache et redémarrage
- `npm run reset` - Nettoyage complet et réinstallation

## 📱 Test sur différents appareils

### Android
- Utiliser l'application Expo Go
- Scanner le QR code
- Si problème, essayer le mode tunnel

### iOS
- Utiliser l'application Expo Go ou l'appareil photo
- Scanner le QR code
- Accepter l'ouverture dans Expo Go

## 🆘 Si rien ne fonctionne

1. Redémarrer complètement :
   ```bash
   npm run reset
   ```

2. Vérifier les versions :
   ```bash
   node --version  # Doit être >= 18
   npm --version
   npx expo --version
   ```

3. Réinstaller Expo CLI :
   ```bash
   npm uninstall -g @expo/cli
   npm install -g @expo/cli@latest
   ```

4. Contacter le support avec les informations du diagnostic :
   ```bash
   npm run diagnostic
   ```
