// Fichier temporaire pour désactiver les modules problématiques
import { Platform } from 'react-native';

console.log('Modules problématiques désactivés temporairement');

// Désactiver complètement les erreurs problématiques
if (Platform.OS !== 'web') {
  // Intercepter et filtrer tous les logs
  const originalWarn = console.warn;
  const originalError = console.error;
  const originalLog = console.log;

  const shouldIgnore = (message) => {
    if (typeof message !== 'string') return false;
    return (
      message.includes('NativeUnimoduleProxy') ||
      message.includes('registerScreenCaptureObserver') ||
      message.includes('DETECT_SCREEN_CAPTURE') ||
      message.includes('Permission Denial') ||
      message.includes('SecurityException') ||
      message.includes('HostObject::get') ||
      message.includes('java.lang.SecurityException')
    );
  };

  console.warn = (...args) => {
    if (shouldIgnore(args[0])) return;
    originalWarn.apply(console, args);
  };

  console.error = (...args) => {
    if (shouldIgnore(args[0])) return;
    originalError.apply(console, args);
  };

  console.log = (...args) => {
    if (shouldIgnore(args[0])) return;
    originalLog.apply(console, args);
  };

  // Intercepter les erreurs globales
  const originalErrorHandler = global.ErrorUtils?.getGlobalHandler?.();
  if (global.ErrorUtils && originalErrorHandler) {
    global.ErrorUtils.setGlobalHandler((error, isFatal) => {
      if (shouldIgnore(error?.message || error?.toString?.())) {
        return; // Ignorer l'erreur
      }
      originalErrorHandler(error, isFatal);
    });
  }
}

// Mock pour expo-av
if (typeof global !== 'undefined') {
  global.Audio = {
    setAudioModeAsync: () => Promise.resolve(),
    Sound: {
      createAsync: () => Promise.resolve({ sound: null, status: {} }),
    },
  };
}

// Mock pour expo-camera
if (typeof global !== 'undefined') {
  global.Camera = {
    requestCameraPermissionsAsync: () => Promise.resolve({ status: 'granted' }),
  };
}

// Mock pour @react-native-voice/voice
if (typeof global !== 'undefined') {
  global.Voice = {
    start: () => Promise.resolve(),
    stop: () => Promise.resolve(),
    destroy: () => Promise.resolve(),
    removeAllListeners: () => {},
    onSpeechResults: () => {},
    onSpeechError: () => {},
  };
}