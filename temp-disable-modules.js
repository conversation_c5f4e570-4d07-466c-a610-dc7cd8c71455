// Fichier temporaire pour désactiver les modules problématiques
import { Platform } from 'react-native';

console.log('Modules problématiques désactivés temporairement');

// Désactiver les warnings de modules natifs sur mobile
if (Platform.OS !== 'web') {
  // Supprimer les warnings de console pour les modules non trouvés
  const originalWarn = console.warn;
  console.warn = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('NativeUnimoduleProxy') ||
       args[0].includes('registerScreenCaptureObserver') ||
       args[0].includes('DETECT_SCREEN_CAPTURE'))
    ) {
      return; // Ignorer ces warnings spécifiques
    }
    originalWarn.apply(console, args);
  };

  const originalError = console.error;
  console.error = (...args) => {
    if (
      typeof args[0] === 'string' &&
      (args[0].includes('NativeUnimoduleProxy') ||
       args[0].includes('registerScreenCaptureObserver') ||
       args[0].includes('DETECT_SCREEN_CAPTURE'))
    ) {
      return; // Ignorer ces erreurs spécifiques
    }
    originalError.apply(console, args);
  };
}

// Mock pour expo-av
if (typeof global !== 'undefined') {
  global.Audio = {
    setAudioModeAsync: () => Promise.resolve(),
    Sound: {
      createAsync: () => Promise.resolve({ sound: null, status: {} }),
    },
  };
}

// Mock pour expo-camera
if (typeof global !== 'undefined') {
  global.Camera = {
    requestCameraPermissionsAsync: () => Promise.resolve({ status: 'granted' }),
  };
}

// Mock pour @react-native-voice/voice
if (typeof global !== 'undefined') {
  global.Voice = {
    start: () => Promise.resolve(),
    stop: () => Promise.resolve(),
    destroy: () => Promise.resolve(),
    removeAllListeners: () => {},
    onSpeechResults: () => {},
    onSpeechError: () => {},
  };
}