// Fichier temporaire pour désactiver les modules problématiques
console.log('Modules problématiques désactivés temporairement');

// Mock pour expo-av
if (typeof global !== 'undefined') {
  global.Audio = {
    setAudioModeAsync: () => Promise.resolve(),
    Sound: {
      createAsync: () => Promise.resolve({ sound: null, status: {} }),
    },
  };
}

// Mock pour expo-camera
if (typeof global !== 'undefined') {
  global.Camera = {
    requestCameraPermissionsAsync: () => Promise.resolve({ status: 'granted' }),
  };
}

// Mock pour @react-native-voice/voice
if (typeof global !== 'undefined') {
  global.Voice = {
    start: () => Promise.resolve(),
    stop: () => Promise.resolve(),
    destroy: () => Promise.resolve(),
    removeAllListeners: () => {},
    onSpeechResults: () => {},
    onSpeechError: () => {},
  };
}