// Point d'entrée simplifié pour DULU Finance Manager
import { registerRootComponent } from 'expo';
import { Platform } from 'react-native';

// Désactiver les erreurs problématiques AVANT tout import
if (Platform.OS !== 'web') {
  const originalWarn = console.warn;
  const originalError = console.error;

  console.warn = (...args) => {
    const message = args[0]?.toString() || '';
    if (
      message.includes('NativeUnimoduleProxy') ||
      message.includes('DETECT_SCREEN_CAPTURE') ||
      message.includes('Permission Denial') ||
      message.includes('SecurityException')
    ) {
      return;
    }
    originalWarn.apply(console, args);
  };

  console.error = (...args) => {
    const message = args[0]?.toString() || '';
    if (
      message.includes('NativeUnimoduleProxy') ||
      message.includes('DETECT_SCREEN_CAPTURE') ||
      message.includes('Permission Denial') ||
      message.includes('SecurityException')
    ) {
      return;
    }
    originalError.apply(console, args);
  };
}

// Importer l'application principale après avoir configuré les filtres
import App from './app/_layout';

// Enregistrer le composant racine
registerRootComponent(App);
